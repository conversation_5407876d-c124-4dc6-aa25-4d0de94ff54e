
/* CSS Variables for Theme System */
:root {
    /* Default Dark Purple Theme */
    --primary-bg-1: #12091c;
    --primary-bg-2: #080324d1;
    --primary-bg-3: #110236;
    --secondary-bg-1: #12051d;
    --secondary-bg-2: #100522;
    --secondary-bg-3: #170425;
    --text-color: #ffffff;
    --button-bg: rgba(0, 0, 0, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(76, 175, 80, 0.3);
    --control-border: rgba(76, 175, 80, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: wheat;
    --back-button-text: #0d0909;
}

/* Ocean Blue Theme */
.theme-ocean {
    --primary-bg-1: #0f3460;
    --primary-bg-2: #16537e;
    --primary-bg-3: #1e6091;
    --secondary-bg-1: #0a2647;
    --secondary-bg-2: #144272;
    --secondary-bg-3: #205295;
    --text-color: #ffffff;
    --button-bg: rgba(15, 52, 96, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(30, 96, 145, 0.3);
    --control-border: rgba(30, 96, 145, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #87ceeb;
    --back-button-text: #0a2647;
}

/* Forest Green Theme */
.theme-forest {
    --primary-bg-1: #1b4332;
    --primary-bg-2: #2d5016;
    --primary-bg-3: #40531b;
    --secondary-bg-1: #081c15;
    --secondary-bg-2: #1b4332;
    --secondary-bg-3: #2d5016;
    --text-color: #ffffff;
    --button-bg: rgba(27, 67, 50, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(64, 83, 27, 0.3);
    --control-border: rgba(64, 83, 27, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #90ee90;
    --back-button-text: #081c15;
}

/* Sunset Orange Theme */
.theme-sunset {
    --primary-bg-1: #8b2635;
    --primary-bg-2: #a0522d;
    --primary-bg-3: #cd853f;
    --secondary-bg-1: #722f37;
    --secondary-bg-2: #8b4513;
    --secondary-bg-3: #a0522d;
    --text-color: #ffffff;
    --button-bg: rgba(139, 38, 53, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(205, 133, 63, 0.3);
    --control-border: rgba(205, 133, 63, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #ffa500;
    --back-button-text: #722f37;
}

/* Light Theme */
.theme-light {
    --primary-bg-1: #f0f8ff;
    --primary-bg-2: #e6f3ff;
    --primary-bg-3: #ddeeff;
    --secondary-bg-1: #ffffff;
    --secondary-bg-2: #f5f5f5;
    --secondary-bg-3: #e0e0e0;
    --text-color: #333333;
    --button-bg: rgba(255, 255, 255, 0.8);
    --button-bg-hover: rgba(0, 0, 0, 0.1);
    --button-border: rgba(0, 0, 0, 0.2);
    --button-border-hover: rgba(0, 0, 0, 0.4);
    --control-bg: rgba(76, 175, 80, 0.2);
    --control-border: rgba(76, 175, 80, 0.4);
    --video-border: #cccccc;
    --back-button-bg: #4a90e2;
    --back-button-text: #ffffff;
}

/* Neo Brutalist Theme */
.theme-brutalist {
    --primary-bg-1: #000000;
    --primary-bg-2: #ffffff;
    --primary-bg-3: #ffff00;
    --secondary-bg-1: #000000;
    --secondary-bg-2: #ffffff;
    --secondary-bg-3: #ffff00;
    --text-color: #000000;
    --button-bg: #ffffff;
    --button-bg-hover: #ffff00;
    --button-border: #000000;
    --button-border-hover: #000000;
    --control-bg: #ffffff;
    --control-border: #000000;
    --video-border: #000000;
    --back-button-bg: #ffff00;
    --back-button-text: #000000;
}

body {
    position: relative;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
    overflow: hidden;
    background: linear-gradient(45deg, var(--primary-bg-1), var(--primary-bg-2), var(--primary-bg-3));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    color: var(--text-color);
    transition: all 0.5s ease;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}


/* Modern gradient background */
body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--secondary-bg-1), var(--secondary-bg-2), var(--secondary-bg-3));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    z-index: -2;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
}

#join-btn {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--button-bg), rgba(0, 0, 0, 0.4));
    color: var(--text-color);
    font-size: 18px;
    font-weight: bold;
    padding: 15px 30px;
    border: 2px solid var(--button-border);
    border-radius: 30px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    outline: none;
    touch-action: manipulation;
    z-index: 1000;
    backdrop-filter: blur(8px);
    animation: pulse 2s infinite;
}

#join-btn i {
    font-size: 20px;
    margin-right: 8px;
    transition: transform 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Hover effect */
#join-btn:hover {
    background: linear-gradient(135deg, var(--button-bg-hover), rgba(255, 255, 255, 0.05));
    border: 2px solid var(--button-border-hover);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
    transform: translate(-50%, -50%) scale(1.05);
}

#join-btn:hover i {
    transform: rotate(15deg) scale(1.1);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Touch and click effect */
#join-btn:active {
    background-color: #e8ece8; /* Even darker background on click */
    box-shadow: 0 3px 4px rgba(0, 0, 0, 0.3); /* Reduced shadow on press */
    transform: translateY(2px); /* Slightly press down */
    border: 2px solid #ffffff; /* Keep light yellow border on press */
}

  
  /* Media query for screens wider than 456px (mobile view) */
  @media (min-width: 456px) {
    #join-btn {
      font-size: 16px; /* Slightly smaller text */
      padding: 10px 20px; /* Adjust padding */
      width: auto; /* Ensure button is adaptive */
      max-width: 300px; /* Limit max width for better visuals */
      border-radius: 12px; /* More rounded corners for mobile */
    }
  }
  
  /* General responsiveness for screens above mobile */
  @media (min-width: 769px) {
    #join-btn {
      font-size: 20px; /* Larger text for bigger screens */
      padding: 14px 28px; /* More padding for emphasis */
    }
  }
  


/* Video Streams Layout - Enhanced for Multiple Members */
#video-streams {
    display: none; /* Hidden by default */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    padding: 8px;
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    max-width: none;
    margin: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.5) transparent;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y pinch-zoom;
    scroll-snap-type: y proximity;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;

    /* Enhanced grid behavior for multiple members - Square layout */
    grid-auto-rows: minmax(120px, 1fr);
    align-content: start;
    justify-content: center;
    align-items: start;

    /* Smooth transitions for grid changes */
    transition: grid-template-columns 0.3s ease, gap 0.3s ease;
}

/* Show video streams when in call */
#video-streams.active {
    display: grid;
}

/* Hide join button when video streams are active */
#video-streams.active ~ #join-btn {
    display: none !important;
}

/* Adjust body when in video call mode */
body:has(#video-streams.active) {
    overflow: hidden;
}

/* Fallback for browsers that don't support :has() */
.video-call-active {
    overflow: hidden !important;
}

/* Enhanced Scrollbar Styling */
#video-streams::-webkit-scrollbar {
    width: 8px;
}

#video-streams::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

#video-streams::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.3));
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#video-streams::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
}

/* Dynamic Grid Layouts for Different Member Counts - Square Shape Optimized */
#video-streams[data-members="1"] {
    grid-template-columns: 1fr;
    justify-items: center;
    align-items: center;
}

#video-streams[data-members="1"] .video-container {
    width: min(80vw, 80vh);
    height: min(80vw, 80vh);
    max-width: 600px;
    max-height: 600px;
    padding-top: 0;
}

#video-streams[data-members="2"] {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: 1fr;
}

#video-streams[data-members="3"],
#video-streams[data-members="4"] {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

#video-streams[data-members="5"],
#video-streams[data-members="6"] {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

#video-streams[data-members="7"],
#video-streams[data-members="8"],
#video-streams[data-members="9"] {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
}

#video-streams[data-members="10"],
#video-streams[data-members="11"],
#video-streams[data-members="12"],
#video-streams[data-members="13"],
#video-streams[data-members="14"],
#video-streams[data-members="15"],
#video-streams[data-members="16"] {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
}

#video-streams[data-members="17"],
#video-streams[data-members="18"],
#video-streams[data-members="19"],
#video-streams[data-members="20"],
#video-streams[data-members="21"],
#video-streams[data-members="22"],
#video-streams[data-members="23"],
#video-streams[data-members="24"],
#video-streams[data-members="25"] {
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
}

/* For 26-36 members: 6x6 grid */
#video-streams[data-members^="2"][data-members$="6"],
#video-streams[data-members^="2"][data-members$="7"],
#video-streams[data-members^="2"][data-members$="8"],
#video-streams[data-members^="2"][data-members$="9"],
#video-streams[data-members^="3"][data-members$="0"],
#video-streams[data-members^="3"][data-members$="1"],
#video-streams[data-members^="3"][data-members$="2"],
#video-streams[data-members^="3"][data-members$="3"],
#video-streams[data-members^="3"][data-members$="4"],
#video-streams[data-members^="3"][data-members$="5"],
#video-streams[data-members^="3"][data-members$="6"] {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, 1fr);
}

/* For 37-49 members: 7x7 grid */
#video-streams[data-members^="3"][data-members$="7"],
#video-streams[data-members^="3"][data-members$="8"],
#video-streams[data-members^="3"][data-members$="9"],
#video-streams[data-members^="4"][data-members$="0"],
#video-streams[data-members^="4"][data-members$="1"],
#video-streams[data-members^="4"][data-members$="2"],
#video-streams[data-members^="4"][data-members$="3"],
#video-streams[data-members^="4"][data-members$="4"],
#video-streams[data-members^="4"][data-members$="5"],
#video-streams[data-members^="4"][data-members$="6"],
#video-streams[data-members^="4"][data-members$="7"],
#video-streams[data-members^="4"][data-members$="8"],
#video-streams[data-members^="4"][data-members$="9"] {
    grid-template-columns: repeat(7, 1fr);
    grid-template-rows: repeat(7, 1fr);
}

/* For 50-64 members: 8x8 grid */
#video-streams[data-members^="5"],
#video-streams[data-members^="6"][data-members$="0"],
#video-streams[data-members^="6"][data-members$="1"],
#video-streams[data-members^="6"][data-members$="2"],
#video-streams[data-members^="6"][data-members$="3"],
#video-streams[data-members^="6"][data-members$="4"] {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(8, 1fr);
}

/* For 65-81 members: 9x9 grid */
#video-streams[data-members^="6"][data-members$="5"],
#video-streams[data-members^="6"][data-members$="6"],
#video-streams[data-members^="6"][data-members$="7"],
#video-streams[data-members^="6"][data-members$="8"],
#video-streams[data-members^="6"][data-members$="9"],
#video-streams[data-members^="7"],
#video-streams[data-members^="8"][data-members$="0"],
#video-streams[data-members^="8"][data-members$="1"] {
    grid-template-columns: repeat(9, 1fr);
    grid-template-rows: repeat(9, 1fr);
}

/* For 82-100 members: 10x10 grid */
#video-streams[data-members^="8"][data-members$="2"],
#video-streams[data-members^="8"][data-members$="3"],
#video-streams[data-members^="8"][data-members$="4"],
#video-streams[data-members^="8"][data-members$="5"],
#video-streams[data-members^="8"][data-members$="6"],
#video-streams[data-members^="8"][data-members$="7"],
#video-streams[data-members^="8"][data-members$="8"],
#video-streams[data-members^="8"][data-members$="9"],
#video-streams[data-members^="9"],
#video-streams[data-members="100"] {
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(10, 1fr);
}

/* Fallback for any other member counts */
#video-streams:not([data-members="1"]):not([data-members="2"]):not([data-members="3"]):not([data-members="4"]):not([data-members="5"]):not([data-members="6"]):not([data-members="7"]):not([data-members="8"]):not([data-members="9"]) {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    grid-auto-rows: minmax(120px, 1fr);
}

/* Dynamic sizing for very large member counts */
#video-streams[data-members^="1"] .video-container,
#video-streams[data-members^="2"] .video-container,
#video-streams[data-members^="3"] .video-container,
#video-streams[data-members^="4"] .video-container,
#video-streams[data-members^="5"] .video-container,
#video-streams[data-members^="6"] .video-container,
#video-streams[data-members^="7"] .video-container,
#video-streams[data-members^="8"] .video-container,
#video-streams[data-members^="9"] .video-container,
#video-streams[data-members="100"] .video-container {
    min-width: 80px;
    font-size: 10px;
}

/* Adjust video container text for large member counts */
#video-streams[data-members^="5"] .video-container::after,
#video-streams[data-members^="6"] .video-container::after,
#video-streams[data-members^="7"] .video-container::after,
#video-streams[data-members^="8"] .video-container::after,
#video-streams[data-members^="9"] .video-container::after,
#video-streams[data-members="100"] .video-container::after {
    font-size: 8px;
    padding: 2px 4px;
}

/* Ultra compact mode for 100 members */
#video-streams[data-members="100"] {
    gap: 4px;
    padding: 4px;
}

#video-streams[data-members="100"] .video-container {
    min-width: 60px;
    border-width: 0.5px;
    border-radius: 4px;
}

#video-streams[data-members="100"] .video-container::after {
    font-size: 6px;
    padding: 1px 2px;
}

/* Member count indicator */
#video-streams::before {
    content: attr(data-members) " member" attr(data-members-plural);
    position: absolute;
    top: 10px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    z-index: 100;
    backdrop-filter: blur(4px);
    opacity: 0.9;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

#video-streams[data-members="1"]::before {
    content: "1 member";
}

#video-streams:not([data-members="1"])::before {
    content: attr(data-members) " members";
}

/* Scroll indicators for overflow */
#video-streams.has-overflow::after {
    content: "↕ Scroll for more";
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 10px;
    z-index: 100;
    opacity: 0.7;
    pointer-events: none;
    animation: fadeInOut 3s infinite;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
}

/* Enhanced Square-Shaped Video Container for Multiple Members */
.video-container {
    position: relative;
    width: 100%;
    padding-top: 100%; /* Perfect 1:1 square aspect ratio */
    background-color: #000000;
    border: 1px solid var(--video-border);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    will-change: transform;
    backface-visibility: hidden;
    scroll-snap-align: start;

    /* Enhanced for better member display */
    min-width: 80px;
    max-width: none;
    margin: 0;

    /* User info overlay */
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.video-container:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 6px;
    background-color: #000000;
}

/* User name overlay for video containers */
.video-container::after {
    content: attr(data-username);
    position: absolute;
    bottom: 8px;
    left: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    text-align: center;
    backdrop-filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.video-container:hover::after {
    opacity: 1;
}

/* Stream Controls */
#stream-controls {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1.5rem;
    padding: 1.25rem;
    border-radius: 35px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1001;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

#stream-controls button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--control-bg);
    border: 2px solid var(--control-border);
    cursor: pointer;
    margin: 0;
    transition: all 0.3s ease;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 0;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* Stream Controls Button Icons - Enhanced for better visibility */
#stream-controls button i {
    font-size: 24px;
    transition: all 0.3s ease;
    opacity: 0.9;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

#stream-controls button:hover i {
    transform: scale(1.2);
    opacity: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

#stream-controls button.active {
    background: rgba(244, 67, 54, 0.3);
    border-color: rgba(244, 67, 54, 0.5);
}

#stream-controls button.active i {
    color: #f44336;
    opacity: 1;
    text-shadow: 0 2px 4px rgba(244, 67, 54, 0.5);
}

/* Enhanced button hover and active states */
#stream-controls button:hover, #stream-controls button:active {
    background: rgba(76, 175, 80, 0.5);
    border-color: rgba(76, 175, 80, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

#stream-controls button span {
    display: none;
}

#stream-controls button:not(.active):hover {
    background: rgba(76, 175, 80, 0.4);
    border-color: rgba(76, 175, 80, 0.8);
}

#stream-controls button:not(.active):hover i {
    color: #4caf50;
    text-shadow: 0 2px 4px rgba(76, 175, 80, 0.5);
}

#stream-controls button.active:hover {
    background: rgba(244, 67, 54, 0.4);
    border-color: rgba(244, 67, 54, 0.8);
}

#stream-controls button.active:hover i {
    color: #f44336;
    text-shadow: 0 2px 4px rgba(244, 67, 54, 0.5);
}

button:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
    border-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
}

button:active i {
    transform: scale(1.1);
    opacity: 1;
    text-shadow: 0 2px 6px rgba(255, 255, 255, 0.8);
}

/* Enhanced icon visibility for all themes */
#stream-controls button i {
    filter: contrast(1.2) brightness(1.1);
}

/* Special handling for light theme icons */
.theme-light #stream-controls button i {
    color: #333333;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    filter: contrast(1.3) brightness(0.9);
}

.theme-light #stream-controls button:hover i {
    color: #2e7d32;
    text-shadow: 0 2px 4px rgba(46, 125, 50, 0.3);
}

.theme-light #stream-controls button.active i {
    color: #d32f2f;
    text-shadow: 0 2px 4px rgba(211, 47, 47, 0.3);
}

/* Enhanced Responsive Layout for Different Screen Sizes */

/* Extra Ultra Small Devices (320px and below) */
@media screen and (max-width: 320px) {
    body {
        padding: 2px;
        font-size: 11px;
    }

    #video-streams {
        grid-template-columns: 1fr;
        gap: 4px;
        height: 70vh;
        padding: 6px;
        width: 99%;
        border-radius: 8px;
    }

    .video-container {
        padding-top: 100%;
        border-radius: 4px;
        min-height: 100px;
        max-height: 150px;
        border-width: 1px;
    }

    #stream-controls {
        bottom: 6px;
        padding: 4px 8px;
        gap: 4px;
        border-radius: 15px;
        max-width: 98%;
    }

    #stream-controls button {
        width: 35px;
        height: 35px;
        font-size: 12px;
        min-width: 35px;
    }

    #stream-controls button i {
        font-size: 14px;
    }

    #join-btn {
        font-size: 11px;
        padding: 8px 12px;
        width: 95%;
        max-width: 240px;
    }

    .back-button, .theme-toggle {
        width: 30px;
        height: 30px;
        font-size: 12px;
        top: 6px;
    }

    .theme-menu {
        right: 2px;
        top: 40px;
        min-width: 100px;
        padding: 6px;
        font-size: 10px;
        max-height: 55vh;
    }

    .theme-option {
        padding: 4px 5px;
        font-size: 10px;
    }

    .theme-preview {
        width: 12px;
        height: 12px;
    }
}

/* Ultra Small Devices (321px - 375px) */
@media screen and (max-width: 375px) {
    body {
        padding: 0;
        font-size: 12px;
    }

    #video-streams {
        grid-template-columns: 1fr; /* Single column for ultra small screens */
        gap: 5px;
        height: 100vh;
        padding: 5px;
        width: 100vw;
        touch-action: pan-y pinch-zoom;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: y mandatory;
        overscroll-behavior: contain;
    }

    .video-container {
        padding-top: 100%; /* Maintain square shape on mobile */
        min-width: 120px;
        border-width: 1px;
        border-radius: 6px;
        scroll-snap-align: start;
    }

    #stream-controls {
        bottom: 8px;
        padding: 6px 10px;
        gap: 6px;
        border-radius: 20px;
        flex-wrap: nowrap;
        justify-content: center;
        max-width: 95%;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    #stream-controls::-webkit-scrollbar {
        display: none;
    }

    #stream-controls button {
        width: 40px;
        height: 40px;
        font-size: 14px;
        flex-shrink: 0;
        min-width: 40px;
    }

    #stream-controls button i {
        font-size: 16px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }

    #join-btn {
        font-size: 12px;
        padding: 10px 16px;
        width: 90%;
        max-width: 260px;
        border-radius: 20px;
    }

    .back-button, .theme-toggle {
        width: 35px;
        height: 35px;
        font-size: 14px;
        top: 8px;
    }

    .back-button {
        left: 8px;
    }

    .theme-toggle {
        right: 8px;
    }

    .theme-menu {
        right: 3px;
        top: 50px;
        min-width: 110px;
        padding: 8px;
        font-size: 11px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .theme-option {
        padding: 5px 6px;
        font-size: 11px;
    }

    .theme-preview {
        width: 14px;
        height: 14px;
    }
}

/* Small Devices (376px - 480px) */
@media screen and (min-width: 376px) and (max-width: 480px) {
    body {
        padding: 4px;
        font-size: 13px;
    }

    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 8px;
        height: 78vh;
        padding: 10px;
        width: 96%;
        touch-action: pan-y pinch-zoom;
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: y proximity;
        overscroll-behavior: contain;
    }

    .video-container {
        padding-top: 100%; /* Perfect square */
        border-width: 1px;
        border-radius: 8px;
        scroll-snap-align: start;
        min-height: 140px;
        max-height: 220px;
    }

    #stream-controls {
        bottom: 10px;
        padding: 8px 12px;
        gap: 8px;
        border-radius: 25px;
        flex-wrap: nowrap;
        justify-content: center;
        max-width: 92%;
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    }

    #stream-controls::-webkit-scrollbar {
        height: 3px;
    }

    #stream-controls::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }

    #stream-controls button {
        width: 45px;
        height: 45px;
        font-size: 16px;
        flex-shrink: 0;
        min-width: 45px;
    }

    #stream-controls button i {
        font-size: 18px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 14px;
        padding: 12px 20px;
        width: 88%;
        max-width: 280px;
    }

    .back-button, .theme-toggle {
        width: 40px;
        height: 40px;
        font-size: 16px;
        top: 10px;
    }

    .back-button {
        left: 10px;
    }

    .theme-toggle {
        right: 10px;
    }

    .theme-menu {
        right: 5px;
        top: 55px;
        min-width: 120px;
        padding: 10px;
        font-size: 12px;
        max-height: 65vh;
        overflow-y: auto;
    }

    .theme-option {
        padding: 6px 8px;
        font-size: 12px;
    }

    .theme-preview {
        width: 16px;
        height: 16px;
    }
}

/* Medium Devices (481px to 600px) */
@media screen and (min-width: 481px) and (max-width: 600px) {
    body {
        padding: 6px;
        font-size: 14px;
    }

    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        height: 82vh;
        padding: 12px;
        width: 94%;
        touch-action: pan-y pinch-zoom;
        scroll-snap-type: y proximity;
        overscroll-behavior: contain;
    }

    .video-container {
        padding-top: 100%; /* Perfect square */
        border-width: 1px;
        border-radius: 10px;
        scroll-snap-align: start;
        min-height: 150px;
        max-height: 250px;
    }

    #stream-controls {
        bottom: 12px;
        padding: 10px 14px;
        gap: 10px;
        border-radius: 28px;
        max-width: 90%;
        overflow-x: auto;
        scrollbar-width: thin;
    }

    #stream-controls button {
        width: 48px;
        height: 48px;
        font-size: 17px;
        flex-shrink: 0;
    }

    #stream-controls button i {
        font-size: 19px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 15px;
        padding: 13px 22px;
        max-width: 300px;
    }

    .back-button, .theme-toggle {
        width: 42px;
        height: 42px;
        font-size: 17px;
        top: 12px;
    }

    .theme-menu {
        right: 12px;
        top: 60px;
        min-width: 130px;
        padding: 11px;
        max-height: 70vh;
        overflow-y: auto;
    }
}

/* Large Mobile/Small Tablet (601px to 768px) */
@media screen and (min-width: 601px) and (max-width: 768px) {
    body {
        padding: 8px;
    }

    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 12px;
        height: 85vh;
        padding: 15px;
        width: 92%;
        touch-action: pan-y pinch-zoom;
        scroll-snap-type: y proximity;
        overscroll-behavior: contain;
    }

    .video-container {
        padding-top: 100%; /* Perfect square for better grid layout */
        border-width: 1px;
        border-radius: 12px;
        scroll-snap-align: start;
        min-height: 160px;
        max-height: 280px;
    }

    #stream-controls {
        bottom: 15px;
        padding: 12px 16px;
        gap: 12px;
        border-radius: 30px;
        max-width: 88%;
        overflow-x: auto;
        scrollbar-width: thin;
    }

    #stream-controls button {
        width: 50px;
        height: 50px;
        font-size: 18px;
        flex-shrink: 0;
    }

    #stream-controls button i {
        font-size: 20px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 16px;
        padding: 14px 25px;
        max-width: 320px;
    }

    .back-button, .theme-toggle {
        width: 45px;
        height: 45px;
        font-size: 18px;
        top: 15px;
    }

    .theme-menu {
        right: 15px;
        top: 70px;
        min-width: 140px;
        padding: 12px;
        max-height: 72vh;
        overflow-y: auto;
    }
}

/* Enhanced Landscape Orientation Support */
@media screen and (orientation: landscape) and (max-height: 500px) {
    #video-streams {
        height: 65vh;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 6px;
        padding: 8px;
        width: 98%;
        scroll-snap-type: x mandatory;
        overflow-x: auto;
        overflow-y: hidden;
        position: relative;
    }

    .video-container {
        padding-top: 100%; /* Keep square even in landscape */
        border-radius: 8px;
        scroll-snap-align: start;
        min-width: 120px;
        max-width: 180px;
    }

    #stream-controls {
        flex-direction: row;
        padding: 6px 12px;
        gap: 10px;
        position: fixed;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: auto;
        justify-content: center;
        border-radius: 25px;
        max-width: 90%;
    }

    #stream-controls button {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    #stream-controls button i {
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 14px;
        padding: 10px 20px;
        max-width: 250px;
    }

    .back-button, .theme-toggle {
        width: 35px;
        height: 35px;
        font-size: 14px;
        top: 8px;
    }

    .theme-menu {
        top: 50px;
        min-width: 120px;
        padding: 8px;
        font-size: 12px;
    }
}

/* Landscape orientation for larger screens */
@media screen and (orientation: landscape) and (min-height: 501px) and (max-width: 1024px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        height: 80vh;
        gap: 12px;
        padding: 15px;
    }

    .video-container {
        padding-top: 75%; /* Balanced aspect ratio */
    }

    #stream-controls {
        bottom: 15px;
        padding: 10px 15px;
        gap: 15px;
    }

    #stream-controls button {
        width: 55px;
        height: 55px;
    }
}

/* Enhanced Tablet Support (769px to 1024px) */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
        height: 100vh;
        padding: 10px;
        width: 100vw;
        grid-auto-rows: minmax(140px, 1fr);
    }

    .video-container {
        padding-top: 100%; /* Maintain square shape */
        min-width: 140px;
        border-radius: 8px;
    }

    #video-streams[data-members="1"] .video-container {
        width: min(75vw, 75vh);
        height: min(75vw, 75vh);
        max-width: 700px;
        max-height: 700px;
        padding-top: 0;
    }

    #video-streams[data-members="2"] {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    #video-streams[data-members="3"],
    #video-streams[data-members="4"] {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 12px;
    }

    #stream-controls {
        bottom: 30px;
        padding: 12px 18px;
        gap: 18px;
        border-radius: 35px;
    }

    #stream-controls button {
        width: 55px;
        height: 55px;
        font-size: 20px;
    }

    #stream-controls button i {
        font-size: 22px;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 17px;
        padding: 16px 32px;
        max-width: 350px;
    }

    .back-button, .theme-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
        top: 20px;
        z-index: 1002;
    }

    .theme-menu {
        right: 20px;
        top: 80px;
        min-width: 150px;
        padding: 15px;
        z-index: 1003;
    }
}

/* Enhanced Desktop and Large Screen Support (1025px and up) */
@media screen and (min-width: 1025px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
        height: 100vh;
        padding: 12px;
        width: 100vw;
        max-width: none;
        grid-auto-rows: minmax(150px, 1fr);
    }

    .video-container {
        padding-top: 100%; /* Maintain square shape */
        min-width: 150px;
        border-radius: 8px;
        border-width: 1px;
    }

    #video-streams[data-members="1"] .video-container {
        width: min(70vw, 70vh);
        height: min(70vw, 70vh);
        max-width: 800px;
        max-height: 800px;
        padding-top: 0;
    }

    /* Specific grid sizes for better organization */
    #video-streams[data-members="2"] {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    #video-streams[data-members="3"],
    #video-streams[data-members="4"] {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 15px;
    }

    #video-streams[data-members="5"],
    #video-streams[data-members="6"] {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 12px;
    }

    #video-streams[data-members="7"],
    #video-streams[data-members="8"],
    #video-streams[data-members="9"] {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 10px;
    }

    #stream-controls {
        bottom: 40px;
        padding: 15px 20px;
        gap: 20px;
        border-radius: 40px;
    }

    #stream-controls button {
        width: 65px;
        height: 65px;
        font-size: 22px;
    }

    #stream-controls button i {
        font-size: 26px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 20px;
        padding: 18px 36px;
        max-width: 400px;
    }

    .back-button, .theme-toggle {
        width: 55px;
        height: 55px;
        font-size: 22px;
        top: 25px;
        z-index: 1002;
    }

    .theme-menu {
        right: 25px;
        top: 90px;
        min-width: 160px;
        padding: 18px;
        font-size: 14px;
        z-index: 1003;
    }
}

/* Ultra-wide and 4K Display Support (1441px and up) */
@media screen and (min-width: 1441px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
        padding: 15px;
        max-width: none;
        height: 100vh;
        width: 100vw;
        grid-auto-rows: minmax(180px, 1fr);
    }

    .video-container {
        border-radius: 10px;
        padding-top: 100%; /* Maintain square shape */
        min-width: 180px;
    }

    #video-streams[data-members="1"] .video-container {
        width: min(80vw, 80vh);
        height: min(80vw, 80vh);
        max-width: 1000px;
        max-height: 1000px;
        padding-top: 0;
    }

    #video-streams[data-members="2"] {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    #video-streams[data-members="3"],
    #video-streams[data-members="4"] {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 20px;
    }

    #video-streams[data-members="5"],
    #video-streams[data-members="6"] {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 18px;
    }

    #video-streams[data-members="7"],
    #video-streams[data-members="8"],
    #video-streams[data-members="9"] {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 15px;
    }

    /* For larger member counts, use smaller squares */
    #video-streams[data-members^="1"],
    #video-streams[data-members^="2"],
    #video-streams[data-members^="3"],
    #video-streams[data-members^="4"],
    #video-streams[data-members^="5"],
    #video-streams[data-members^="6"],
    #video-streams[data-members^="7"],
    #video-streams[data-members^="8"],
    #video-streams[data-members^="9"],
    #video-streams[data-members="100"] {
        gap: 8px;
    }

    #stream-controls {
        bottom: 50px;
        padding: 18px 25px;
        gap: 25px;
        border-radius: 45px;
    }

    #stream-controls button {
        width: 70px;
        height: 70px;
        font-size: 24px;
    }

    #stream-controls button i {
        font-size: 28px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    }

    #join-btn {
        font-size: 22px;
        padding: 20px 40px;
        max-width: 450px;
    }
}
/* Touch and Accessibility Enhancements */
@media (hover: none) and (pointer: coarse) {
    /* Touch device optimizations */
    #stream-controls button {
        min-width: 48px;
        min-height: 48px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    #stream-controls button i {
        font-size: 22px !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
    }

    #stream-controls button:active {
        transform: scale(0.95);
        background: rgba(76, 175, 80, 0.7) !important;
    }

    #stream-controls button:active i {
        transform: scale(1.1);
        opacity: 1;
    }

    #join-btn {
        min-height: 48px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .back-button, .theme-toggle {
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .theme-option {
        min-height: 44px;
        touch-action: manipulation;
    }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .video-container {
        border-width: 0.5px;
    }

    #stream-controls {
        border-width: 0.5px;
    }

    .theme-menu {
        border-width: 0.5px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    body::before,
    body::after {
        animation: none !important;
    }

    #join-btn {
        animation: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #ffffff;
        --button-bg: rgba(0, 0, 0, 0.7);
        --button-bg-hover: rgba(255, 255, 255, 0.1);
    }
}

/* Light Mode Support */
@media (prefers-color-scheme: light) {
    :root {
        --text-color: #333333;
        --button-bg: rgba(255, 255, 255, 0.8);
        --button-bg-hover: rgba(0, 0, 0, 0.1);
    }
}

.back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    color: var(--back-button-text);
    background-color: var(--back-button-bg);
    border: none;
    border-radius: 50%;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.back-button:hover {
    background-color: rgb(255, 255, 255); /* Yellow on hover */
    transform: scale(1.1); /* Slightly enlarge the button on hover */
    box-shadow: 0 0 15px 5px rgb(255, 255, 255); /* Glow effect */
}

.back-button:active {
    background-color: #ffffff; /* Slightly darker yellow when clicked (touch effect) */
    transform: scale(1); /* Normal size when pressed */
    box-shadow: 0 0 10px 4px #ffffff; /* Glow effect when clicked */
}

.back-button i {
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: all 0.3s ease;
}

.back-button:hover i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* Theme Toggle Button */
.theme-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    color: var(--back-button-text);
    background-color: var(--back-button-bg);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
    z-index: 1001;
}

.theme-toggle:hover {
    background-color: rgb(255, 255, 255);
    transform: scale(1.1);
    box-shadow: 0 0 15px 5px rgb(255, 255, 255);
}

.theme-toggle:active {
    background-color: #ffffff;
    transform: scale(1);
    box-shadow: 0 0 10px 4px #ffffff;
}

.theme-toggle i {
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: all 0.3s ease;
}

.theme-toggle:hover i {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* Theme Menu */
.theme-menu {
    position: absolute;
    top: 80px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    display: none;
    flex-direction: column;
    gap: 10px;
    z-index: 1002;
    min-width: 150px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-menu.show {
    display: flex;
}

.theme-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.theme-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.theme-option.active {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.theme-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.theme-preview-default {
    background: linear-gradient(45deg, #12091c, #080324d1, #110236);
}

.theme-preview-ocean {
    background: linear-gradient(45deg, #0f3460, #16537e, #1e6091);
}

.theme-preview-forest {
    background: linear-gradient(45deg, #1b4332, #2d5016, #40531b);
}

.theme-preview-sunset {
    background: linear-gradient(45deg, #8b2635, #a0522d, #cd853f);
}

.theme-preview-light {
    background: linear-gradient(45deg, #f0f8ff, #e6f3ff, #ddeeff);
}

.theme-preview-brutalist {
    background: linear-gradient(45deg, #000000, #ffffff, #ffff00);
}

/* Neo Brutalist Specific Overrides */
.theme-brutalist body {
    background: #ffffff !important;
    background-image: none !important;
    animation: none !important;
    font-family: 'Courier New', monospace !important;
}

.theme-brutalist body::before,
.theme-brutalist body::after {
    display: none !important;
}

.theme-brutalist #join-btn {
    background: #ffff00 !important;
    color: #000000 !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 8px 8px 0 #000000 !important;
    animation: none !important;
    font-family: 'Courier New', monospace !important;
    font-weight: 900 !important;
    text-transform: uppercase !important;
    letter-spacing: 2px !important;
}

.theme-brutalist #join-btn:hover {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 4px solid #ffff00 !important;
    box-shadow: 8px 8px 0 #ffff00 !important;
    transform: translate(-50%, -50%) translate(-4px, -4px) !important;
}

.theme-brutalist #video-streams {
    background: #ffffff !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #000000 !important;
}

.theme-brutalist .video-container {
    background: #ffff00 !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 4px 4px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist .video-container:hover {
    transform: translate(-2px, -2px) !important;
    box-shadow: 6px 6px 0 #000000 !important;
}

.theme-brutalist #stream-controls {
    background: #000000 !important;
    border: 4px solid #ffff00 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #ffff00 !important;
}

.theme-brutalist #stream-controls button {
    background: #ffffff !important;
    border: 3px solid #000000 !important;
    border-radius: 0 !important;
    color: #000000 !important;
    box-shadow: 3px 3px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist #stream-controls button:hover {
    background: #ffff00 !important;
    transform: translate(-2px, -2px) scale(1) !important;
    box-shadow: 5px 5px 0 #000000 !important;
}

.theme-brutalist #stream-controls button.active {
    background: #ff0000 !important;
    color: #ffffff !important;
    border: 3px solid #000000 !important;
}

.theme-brutalist .back-button,
.theme-brutalist .theme-toggle {
    background: #ffff00 !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 4px 4px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist .back-button:hover,
.theme-brutalist .theme-toggle:hover {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 3px solid #ffff00 !important;
    box-shadow: 4px 4px 0 #ffff00 !important;
    transform: translate(-2px, -2px) scale(1) !important;
}

.theme-brutalist .theme-menu {
    background: #ffffff !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #000000 !important;
}

.theme-brutalist .theme-option {
    color: #000000 !important;
    border-radius: 0 !important;
    font-family: 'Courier New', monospace !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
}

.theme-brutalist .theme-option:hover {
    background: #ffff00 !important;
    transform: none !important;
    border: 2px solid #000000 !important;
}

.theme-brutalist .theme-option.active {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 2px solid #ffff00 !important;
}
